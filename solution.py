def solution(board):
    """
    Simulates falling boxes and explosions in a gravity simulation.

    Args:
        board: 2D list where:
            '-' = empty cell
            '*' = obstacle
            '#' = box

    Returns:
        2D list representing the final state after all boxes have fallen and explosions occurred
    """
    if not board or not board[0]:
        return board

    rows, cols = len(board), len(board[0])

    # Convert to mutable format
    board = [list(row) for row in board]

    while True:
        # Step 1: Apply gravity to all columns
        new_board = apply_gravity_v2(board)

        # Step 2: Handle explosions
        exploded_board, explosions_occurred = handle_explosions_v2(new_board)

        # Check if anything changed
        if not explosions_occurred and boards_equal(exploded_board, board):
            break

        board = exploded_board

    return board


def apply_gravity_v2(board):
    """Apply gravity using the approach from your former solution but fixed."""
    rows, cols = len(board), len(board[0])
    new_board = [row[:] for row in board]

    # Step 1: Apply gravity column by column
    for col in range(cols):
        stack = []
        for row in range(rows - 1, -1, -1):  # From bottom to top
            if board[row][col] == "*":
                # Place all collected boxes above this obstacle
                pos = row - 1
                for _ in stack:
                    if pos >= 0:
                        new_board[pos][col] = '#'
                        pos -= 1

                # Clear the area above the placed boxes
                for r in range(pos, -1, -1):
                    if new_board[r][col] != '*':
                        new_board[r][col] = '-'
                stack = []
            elif board[row][col] == '#':
                stack.append(row)
                new_board[row][col] = '-'  # Clear the original position

        # Handle boxes that don't hit any obstacle (fall to bottom)
        pos = rows - 1
        for _ in stack:
            new_board[pos][col] = '#'
            pos -= 1
        # Clear remaining positions above
        for r in range(pos, -1, -1):
            if new_board[r][col] != '*':
                new_board[r][col] = '-'

    return new_board


def boards_equal(board1, board2):
    """Check if two boards are equal."""
    if len(board1) != len(board2):
        return False
    for i in range(len(board1)):
        if board1[i] != board2[i]:
            return False
    return True


def handle_explosions_v2(board):
    """Handle explosions when boxes hit obstacles - based on your approach but fixed."""
    rows, cols = len(board), len(board[0])
    exploded = [[False] * cols for _ in range(rows)]

    # Step 2: Mark exploded boxes (based on your approach but with the bug fix)
    directions = [(-1, -1), (-1, 0), (-1, 1), (0, -1), (0, 0), (0, 1), (1, -1), (1, 0), (1, 1)]
    explosions_occurred = False

    for r in range(rows):
        for c in range(cols):
            if board[r][c] == '*':
                if r > 0 and board[r-1][c] == '#':
                    explosions_occurred = True
                    for dr, dc in directions:
                        nr, nc = r + dr, c + dc
                        # Fixed the bug: was 0<=nr<=rows, should be 0<=nr<rows
                        if 0 <= nr < rows and 0 <= nc < cols and board[nr][nc] == '#':
                            exploded[nr][nc] = True
                    exploded[r-1][c] = True

    # Step 3: Remove exploded boxes
    new_board = [row[:] for row in board]
    for r in range(rows):
        for c in range(cols):
            if exploded[r][c]:
                new_board[r][c] = '-'

    return new_board, explosions_occurred


# Test with the provided test cases
if __name__ == "__main__":
    # Test case 1 from test-cases.txt
    test1 = [["#","-","#","#","*"],
             ["#","-","-","#","#"],
             ["-","#","-","#","-"],
             ["-","-","#","-","#"],
             ["#","*","-","-","-"],
             ["-","-","*","#","-"]]

    expected1 = [["-","-","-","-","*"],
                 ["-","-","-","-","-"],
                 ["-","-","-","-","-"],
                 ["-","-","-","-","-"],
                 ["-","*","-","-","#"],
                 ["#","-","*","-","#"]]

    result1 = solution(test1)
    print("Test 1:")
    print("Match:", result1 == expected1)
    if result1 != expected1:
        print("Result:")
        for row in result1:
            print(row)
        print("Expected:")
        for row in expected1:
            print(row)
    print()

    # Test case 2 from test-cases.txt
    test2 = [["#","#","*"],
             ["#","-","*"],
             ["#","-","*"],
             ["-","#","#"],
             ["*","-","#"],
             ["*","-","-"],
             ["*","-","-"]]

    expected2 = [["-","-","*"],
                 ["-","-","*"],
                 ["-","-","*"],
                 ["-","-","-"],
                 ["*","-","-"],
                 ["*","-","#"],
                 ["*","-","#"]]

    result2 = solution(test2)
    print("Test 2:")
    print("Match:", result2 == expected2)
    if result2 != expected2:
        print("Result:")
        for row in result2:
            print(row)
        print("Expected:")
        for row in expected2:
            print(row)
    print()
    
    # Test case from more test case.txt
    test3 = [['#', '#', '*'],
             ['#', '-', '*'],
             ['#', '-', '*'],
             ['-', '#', '#'],
             ['*', '-', '#'],
             ['*', '-', '-'],
             ['*', '-', '-']]

    expected3 = [['-', '-', '*'],
                 ['-', '-', '*'],
                 ['-', '-', '*'],
                 ['-', '-', '-'],
                 ['*', '-', '-'],
                 ['*', '-', '#'],
                 ['*', '-', '#']]

    result3 = solution(test3)
    print("Test 3 (from more test case.txt):")
    print("Match:", result3 == expected3)
    if result3 != expected3:
        print("Result:")
        for row in result3:
            print(row)
        print("Expected:")
        for row in expected3:
            print(row)
