def solution(board):
    rows = len(board)
    cols = len(board[0])
    
    board = [list(row) for row in board]
    exploded = [[False]*cols for _ in range(rows)]
    
    # step 1
    for col in range(cols):
        stack = []
        for row in range(rows-1, -1, -1):
            if board[row][col] == "*":
                pos = rows -1
                for box_row in stack:
                    board[pos][col] = '#'
                    pos -=1
                
                for r in range(pos, row, -1):
                    board[r][col] = '-'
                stack = []
            elif board[row][col] == '#':
                stack.append(row)
                
        pos = rows - 1
        for box_row in stack:
            board[pos][col] = '#'
            pos -=1
        for r in range(pos, -1, -1):
            board[r][col] = '-'
            
    # step 2: Mark exploaded boxes
    directions = [(-1,-1), (-1,0), (-1,1), (0,-1), (0,0), (0,1), (1, -1), (1,0), (1, 1)]
    for r in range(rows):
        for c in range(cols):
            if board[r][c] == '*':
                if r>0 and board[r-1][c] == '#':
                    for dr, dc in directions:
                        nr, nc = r+dr, c+dc
                        if 0<=nr<=rows and 0<=nc<cols and board[nr][nc] == '#':
                            exploded[nr][nc] = True
                    exploded[r-1][c] = True
                    
                    
    # step 3
    for r in range(rows):
        for c in range(cols):
            if exploded[r][c]:
                board[r][c] ='-'
                
    
    return [''.join(row) for row in board]
    
    
    
    
    
    
    
    
    
    
    # # step 1
    # for c in range(cols):
    #     stack = []
    #     column = [grid[r][c] for r in range(rows)]
    #     new_col = ['-']*rows
        
    #     obstacle_indices = [i for i, val in enumerate(column) if val == '*']
        
    #     boundaries = [-1] + obstacle_indices + [rows]
        
    #     for i in range(len(boundaries) -1):
    #         start = boundaries[i]
    #         end = boundaries[i+1]
            
    #         # count boxes in this segment
    #         boxes_count = 0
    #         for r in range(start+1, end):
    #             if column[r] == '#':
    #                 boxes_count += 1
                    
    #         pos = end -1
    #         while boxes_count > 0:
    #             new_col[pos] = '#'
    #             boxes_count -= 1
    #             pos -=1
                
    #         if end != rows:
    #             new_col[end] = '*'
                
    #     for i in range(rows):
    #         grid[r][c] = new_col[r]
            
    #  # step 2
    # exploding_obstacles = []
    # for r in range(1, rows):
    #     for c in range(cols):
    #         if grid[r][c] == '*':
    #             if grid[r-1][c] == '#':
    #                 exploding_obstacles.append((r,c))
    
    # if not exploding_obstacles:
    #     return ["".join(row) for row in grid]
        
    # # step 3
    # boxes_to_remove = set() 
    # directions = [(-1, -1), (-1, 0), (-1,1), (0,-1), (0,0), (0,1), (1, -1), (1, 0), (1, 1) ]
    
    # for (r_o, c_o) in exploding_obstacles:
    #     for dr, dc in directions:
    #         rr= r_o + dr
    #         cc = c_o + dc
    #         if 0 <= rr < rows and 0 <=cc <cols:
    #             if grid[rr][cc] == '#':
    #                 boxes_to_remove.add((rr,cc))
                    
    # # Explosions destroy the boxes marked;
    # for (r,c) in boxes_to_remove:
    #     grid[r][c] = '-'
    
    # return [''.join(row) for row in grid]
    
    
    
    
    
           
            
            
            
                        
            
            
            
            
            
            
            
            
            
            