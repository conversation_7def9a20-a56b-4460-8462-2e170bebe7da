test1:

input 1
[["#","-","#","#","*"], 
 ["#","-","-","#","#"], 
 ["-","#","-","#","-"], 
 ["-","-","#","-","#"], 
 ["#","*","-","-","-"], 
 ["-","-","*","#","-"]]


 expected value:
 [["-","-","-","-","*"], 
 ["-","-","-","-","-"], 
 ["-","-","-","-","-"], 
 ["-","-","-","-","-"], 
 ["-","*","-","-","#"], 
 ["#","-","*","-","#"]]




 test 2:
 input:
 [["#","#","*"], 
 ["#","-","*"], 
 ["#","-","*"], 
 ["-","#","#"], 
 ["*","-","#"], 
 ["*","-","-"], 
 ["*","-","-"]]


 expected:
 [["-","-","*"], 
 ["-","-","*"], 
 ["-","-","*"], 
 ["-","-","-"], 
 ["*","-","-"], 
 ["*","-","#"], 
 ["*","-","#"]]



test 3:
input
[["#","#","#"], 
 ["#","*","#"], 
 ["#","#","#"]]


 expected
 [["-","-","-"], 
 ["-","*","-"], 
 ["-","-","-"]]